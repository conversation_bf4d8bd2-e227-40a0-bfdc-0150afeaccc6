import MountPointCmpt from "../../common/MountPointCmpt"
import <PERSON>LandCollider from "./BaseLandCollider"
import { CollisionResult, HeroTrajectory, LandType } from "./Define";

const { ccclass, property } = cc._decorator;

@ccclass
export default class WaterLandCollider extends BaseLandCollider {
    type: LandType = LandType.Water

    @property({ tooltip: "水流间隔时间,单位秒", step: 0.1, type: cc.Float })
    protected interval: number = 1
    @property({ tooltip: "水流持续时间,单位秒", step: 0.1, type: cc.Float })
    protected duration: number = 1
    @property({ tooltip: "首次水流等待时间,单位秒", step: 0.1, type: cc.Float })
    protected delay: number = 0

    boxNode: cc.Node = null
    attachNode: cc.Node = null
    // 原始anchorY
    orgAnchorY: number = null
    enterMountPoint: cc.Node = null
    exitMountPoint: cc.Node = null

    waterSpeed: number = 0
    xMoveSpeed: number = 0
    private timer: number = 0
    private isPlaying: boolean = false

    attachToCenter: boolean = false
    onAttachPosChanged: (x: number, y: number) => boolean = null
    beforeAttachRemove: () => void = null

    private graphics: cc.Graphics = null

    onLoad() {
        const name = this.node.name
        const arg = name.split("_")
        if (arg.length == 3) {
            this.interval = Math.max(1, Number(arg[1])) * ut.Time.Second
            this.duration = Math.max(1, Number(arg[2])) * ut.Time.Second
        }
        if (this.interval == 0) {
            this.interval = 1 * ut.Time.Second
        }
        if (this.duration == 0) {
            this.duration = 1 * ut.Time.Second
        }
        this.delay *= ut.Time.Second
        const sk = this.node.Component(sp.Skeleton)
        sk.playAnimation("jingzhi", true)

        const mount = this.node.Component(MountPointCmpt)
        this.boxNode = mount.getPoint("box")
        this.enterMountPoint = mount.getPoint("guadian_enter")
        this.exitMountPoint = mount.getPoint("guadian_exit")
        if (!this.boxNode || !this.enterMountPoint || !this.exitMountPoint) {
            throw new Error(`WaterLandCollider 检测失败,因为不存在挂点,name:${this.node.name}`)
        }
        this.boxNode.y = 0
        this.orgAnchorY = this.boxNode.anchorY

        this.attachNode = mount.getPoint("attach")
        this.waterSpeed = this.boxNode.height / sk.getAnimationDuration("enter")
        // 0.1s内移动到水流中间
        this.xMoveSpeed = this.boxNode.width / 2 / 0.1

        this.graphics = this.boxNode.addComponent(cc.Graphics)
        this.graphics.lineWidth = 2
        this.graphics.strokeColor = cc.Color.RED
    }

    update(dt: number) {
        this.drawBox()
        this.timer += dt * ut.Time.Second
        if (this.timer < this.delay) return
        this.delay = 0

        if (!this.isPlaying) {
            if (this.timer >= this.interval) {
                this.playLoopAnimation()
            }
        }
        if (!this.isPlaying) return
        let changeX = 0
        if (this.attachToCenter && this.attachNode.childrenCount) {
            const it = this.attachNode.children[0]
            if (it.position.x < 0) {
                changeX = this.xMoveSpeed * dt
            }
            it.x += changeX
        }
        const changeY = -(dt * this.waterSpeed)
        this.attachNode.y += changeY
        const fn = this.onAttachPosChanged
        if (fn && !fn(changeX, changeY)) {
            this.clearAttach(false)
        }
    }

    private drawBox() {
        if (!this.graphics || !this.boxNode) return
        this.graphics.clear()
        const rect = this.boxNode.getBoundingBox()
        this.graphics.rect(rect.x, rect.y, rect.width, rect.height)
        this.graphics.stroke()
    }

    private async playLoopAnimation() {
        this.isPlaying = true
        this.attachNode.y = this.attachNode.x = 0
        const sk = this.node.Component(sp.Skeleton)
        await sk.playAnimation("enter", false)
        sk.playAnimation("loop", true)
        await ut.wait(this.duration, this)
        this.changeMountPoint(this.exitMountPoint)
        await sk.playAnimation("exit", false)
        this.isPlaying = false
        this.changeMountPoint(this.enterMountPoint)
        sk.playAnimation("jingzhi", true)
        this.clearAttach()
        this.timer = 0
    }

    private changeMountPoint(parent: cc.Node) {
        this.boxNode.parent = null
        if (!parent) return
        const isEnter = parent == this.enterMountPoint
        this.boxNode.parent = parent
        this.boxNode.anchorY = isEnter ? 0 : 1
    }

    public intersectWith(trajectory: HeroTrajectory): CollisionResult | null {
        if (!this.isPlaying) return null
        if (!this.boxNode || !this.boxNode.parent) return null
        if (this.boxNode.parent === this.node) return null

        const rect = this.boxNode.getBoundingBoxToWorld()
        if (cc.Intersection.rectPolygon(rect, trajectory.worldPoints)) {
            return { point: trajectory.leftFoot.end, distance: 1 }
        }
        return null
    }

    public isNeedHeroCollider(): boolean { return true }

    private clearAttach(removeEvt: boolean = true) {
        removeEvt && this.beforeAttachRemove?.()
        this.beforeAttachRemove = null
        this.attachToCenter = false
        this.onAttachPosChanged = null
        this.attachNode.removeAllChildren()
    }
}